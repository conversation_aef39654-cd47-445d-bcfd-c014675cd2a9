import { defineConfig } from 'vite';
import monkey from 'vite-plugin-monkey';

// https://vitejs.dev/config/
export default defineConfig({
  // 开发服务器配置
  server: {
    port: 3000,
    open: true,
    cors: true
  },

  // 构建配置优化
  build: {
    // 使用esbuild进行快速压缩
    minify: 'esbuild',
  },


  // 插件配置
  plugins: [
    monkey({
      entry: 'src/main.ts',
      userscript: {
        name: '账号切换助手',
        description: '快速切换账号的油猴脚本',
        version: '1.0.0',
        author: 'Account Change Script',
        icon: 'https://vitejs.dev/logo.svg',
        namespace: 'account-change-script',
        match: [
          'http://localhost:*/*',
          'https://cq24.91cloudpay.com:4443/*',
          'http://cq24.local/*',
          'http://kkl.local/*',
          'https://kkl-dev.91cloudpay.com:4443/*',
          'https://kkl.91cloudpay.com:4443/*',
          'https://************/*'
        ],
        require: [
          'https://cdnjs.cloudflare.com/ajax/libs/jsencrypt/3.3.2/jsencrypt.min.js'
        ],

        'run-at': 'document-start'
      }
    })
  ],

  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    __VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0')
  }
});
