// 账号切换组件样式 - 性能优化版
// 使用CSS自定义属性、优化选择器嵌套、减少代码重复

// ===== CSS自定义属性定义 =====
:host {
  // 基础颜色调色板
  --ac-primary-400: #38bdf8;
  --ac-primary-500: #0ea5e9;
  --ac-primary-600: #0284c7;

  --ac-gray-50: #fafafa;
  --ac-gray-200: #e4e4e7;
  --ac-gray-300: #d4d4d8;
  --ac-gray-400: #a1a1aa;
  --ac-gray-600: #52525b;
  --ac-gray-900: #18181b;

  --ac-success: #22c55e;
  --ac-danger: #ef4444;
  --ac-warning: #f59e0b;
  --ac-info: #3b82f6;

  // 语义化颜色系统（支持主题切换）
  --ac-bg-primary: #fff;
  --ac-bg-secondary: var(--ac-gray-50);
  --ac-bg-glass: rgba(255, 255, 255, 0.95);
  --ac-bg-overlay: rgba(0, 0, 0, 0.3);

  --ac-text-primary: var(--ac-gray-900);
  --ac-text-secondary: var(--ac-gray-600);
  --ac-text-muted: var(--ac-gray-400);
  --ac-text-inverse: #fff;

  --ac-border-light: var(--ac-gray-200);
  --ac-border-medium: var(--ac-gray-300);

  // 尺寸系统（基于rem的响应式设计）
  --ac-radius-sm: 0.375rem;
  --ac-radius-md: 0.5rem;
  --ac-radius-lg: 0.75rem;
  --ac-radius-xl: 1rem;
  --ac-radius-full: 9999px;

  --ac-space-1: 0.25rem;
  --ac-space-2: 0.5rem;
  --ac-space-3: 0.75rem;
  --ac-space-4: 1rem;
  --ac-space-5: 1.25rem;
  --ac-space-6: 1.5rem;
  --ac-space-8: 2rem;

  // 动画系统
  --ac-duration-fast: 0.15s;
  --ac-duration-normal: 0.2s;
  --ac-duration-slow: 0.3s;
  --ac-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ac-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ac-ease-spring: cubic-bezier(0.34, 1.56, 0.64, 1);

  // 阴影系统（优化性能）
  --ac-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --ac-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --ac-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ac-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  // 字体系统
  --ac-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'SF Pro Display', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --ac-font-mono: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  --ac-font-size-xs: 0.75rem;
  --ac-font-size-sm: 0.875rem;
  --ac-font-size-base: 1rem;
  --ac-font-size-lg: 1.125rem;
  --ac-font-size-xl: 1.25rem;

  // Z-index系统
  --ac-z-floating: 10000;
  --ac-z-modal: 10001;
  --ac-z-modal-detail: 10005;
}

// 暗色主题支持（可选）
:host([theme="dark"]) {
  --ac-bg-primary: #1f2937;
  --ac-bg-secondary: #374151;
  --ac-bg-glass: rgba(31, 41, 55, 0.95);
  --ac-bg-overlay: rgba(0, 0, 0, 0.5);

  --ac-text-primary: #f9fafb;
  --ac-text-secondary: #d1d5db;
  --ac-text-muted: #9ca3af;

  --ac-border-light: #4b5563;
  --ac-border-medium: #6b7280;
}

// ===== 优化的混入定义 =====
@mixin ac-button-base {
  border: 1px solid transparent;
  border-radius: var(--ac-radius-md);
  cursor: pointer;
  transition: all var(--ac-duration-normal) var(--ac-ease-out);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--ac-space-2);
  text-decoration: none;
  font-weight: 500;

  &:active {
    transform: scale(0.98);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

@mixin ac-scrollbar {
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: var(--ac-bg-secondary);
    border-radius: var(--ac-radius-sm);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--ac-border-medium);
    border-radius: var(--ac-radius-sm);

    &:hover {
      background: var(--ac-primary-400);
    }
  }
}

// 通用样式类
%ac-card {
  background: var(--ac-bg-primary);
  border: 1px solid var(--ac-border-light);
  border-radius: var(--ac-radius-lg);
  box-shadow: var(--ac-shadow-md);
}

%ac-input {
  padding: var(--ac-space-3) var(--ac-space-4);
  border: 1px solid var(--ac-border-light);
  border-radius: var(--ac-radius-md);
  font-size: var(--ac-font-size-sm);
  background: var(--ac-bg-primary);
  color: var(--ac-text-primary);
  transition: all var(--ac-duration-normal) var(--ac-ease-out);

  &:focus {
    outline: none;
    border-color: var(--ac-primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
  }
}

%ac-text-sm {
  font-size: var(--ac-font-size-sm);
}

%ac-text-xs {
  font-size: var(--ac-font-size-xs);
}

%ac-label {
  font-size: var(--ac-font-size-sm);
  font-weight: 500;
  color: var(--ac-text-primary);
}

%ac-code-block {
  font-family: var(--ac-font-mono);
  background: var(--ac-bg-secondary);
  border: 1px solid var(--ac-border-light);
  border-radius: var(--ac-radius-md);
  padding: var(--ac-space-3);
}

// ===== 基础样式 =====
*, *::before, *::after {
  box-sizing: border-box;
}

:host {
  all: initial;
  font-family: var(--ac-font-family);
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 999999;
  pointer-events: none;
}

// ===== 悬浮按钮 =====
.floating-button {
  position: fixed;
  top: 50%;
  left: -22.5px;
  transform: translateY(-50%);
  z-index: var(--ac-z-floating);
  width: 30px;
  height: 30px;
  background: var(--ac-primary-600);
  border: none;
  border-radius: var(--ac-radius-full);
  cursor: pointer;
  transition: all var(--ac-duration-slow) var(--ac-ease-out);
  box-shadow: var(--ac-shadow-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--ac-text-inverse);
  pointer-events: auto;

  &:hover {
    left: 0;
    transform: translateY(-50%) scale(1.05);
    box-shadow: var(--ac-shadow-lg);
    background: var(--ac-primary-500);
  }

  &:active {
    transform: translateY(-50%) scale(0.95);
    transition: all var(--ac-duration-fast) var(--ac-ease-in-out);
  }
}

.floating-button .icon {
  width: 14px;
  height: 14px;
  fill: currentColor;
  transition: transform var(--ac-duration-normal) var(--ac-ease-out);
}

.floating-button:hover .icon {
  transform: rotate(90deg);
}

// ===== 模态框系统 =====
.modal-overlay {
  position: fixed;
  inset: 0;
  background: var(--ac-bg-overlay);
  backdrop-filter: blur(12px);
  z-index: var(--ac-z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--ac-duration-slow) var(--ac-ease-out);
  pointer-events: auto;
  padding: var(--ac-space-4);

  &.show {
    opacity: 1;
    visibility: visible;
  }
}

.modal-overlay.show .modal {
  transform: scale(1) translateY(0);
}

.modal {
  @extend %ac-card;
  background: var(--ac-bg-glass);
  backdrop-filter: blur(8px);
  box-shadow: var(--ac-shadow-xl);
  width: min(800px, 100%);
  height: min(650px, 90vh);
  overflow: hidden;
  transform: scale(0.95) translateY(10px);
  transition: all var(--ac-duration-slow) var(--ac-ease-spring);
  position: relative;
}

// ===== 模态框头部 =====
.modal-header {
  padding: var(--ac-space-5) var(--ac-space-6) var(--ac-space-4);
  border-bottom: 1px solid var(--ac-border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  background: var(--ac-bg-secondary);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--ac-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// ===== 标签页系统 =====
.header-tabs {
  display: flex;
  gap: var(--ac-space-2);
  flex: 1;
  background: var(--ac-bg-primary);
  border-radius: var(--ac-radius-lg);
  padding: var(--ac-space-1);
}

.header-tab {
  @include ac-button-base;
  @extend %ac-text-sm;
  padding: var(--ac-space-2) var(--ac-space-4);
  color: var(--ac-text-secondary);
  border-radius: var(--ac-radius-md);
  flex: 1;
  text-align: center;

  &.active {
    color: var(--ac-text-inverse);
    background: var(--ac-primary-600);
    box-shadow: var(--ac-shadow-sm);
  }

  &:hover:not(.active) {
    color: var(--ac-text-primary);
    background: var(--ac-bg-secondary);
  }
}

.close-btn {
  @include ac-button-base;
  background: var(--ac-bg-primary);
  border: 1px solid var(--ac-border-light);
  width: 28px;
  height: 28px;
  border-radius: var(--ac-radius-md);
  color: var(--ac-text-secondary);
  margin-left: var(--ac-space-4);
  font-size: 1rem;

  &:hover {
    background: var(--ac-danger);
    border-color: var(--ac-danger);
    color: var(--ac-text-inverse);
  }
}

// ===== 模态框主体 =====
.modal-body {
  padding: var(--ac-space-4);
  background: var(--ac-bg-primary);
  display: flex;
  flex-direction: column;
  @include ac-scrollbar;
}

// ===== 标签页内容 =====
.tab-container.header-mode .tabs {
  display: none;
}

.tabs {
  display: flex;
  border-bottom: 1px solid var(--ac-border-light);
  background: var(--ac-bg-secondary);
  border-radius: var(--ac-radius-md) var(--ac-radius-md) 0 0;
  padding: 0 var(--ac-space-2);
}

.tab {
  @include ac-button-base;
  @extend %ac-text-sm;
  padding: var(--ac-space-3) var(--ac-space-5);
  color: var(--ac-text-secondary);
  border-bottom: 2px solid transparent;
  border-radius: 0;

  &.active {
    color: var(--ac-primary-600);
    border-bottom-color: var(--ac-primary-600);
  }

  &:hover:not(.active) {
    color: var(--ac-text-primary);
    background: var(--ac-bg-primary);
  }
}

.request-detail-modal .tab-contents {
  overflow-y: auto;
}

.tab-content {
  display: none;
  animation: ac-fadeIn 0.2s var(--ac-ease-out);

  &.active {
    display: block;
  }
}

@keyframes ac-fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// ===== 表单系统 =====
.form-section {
  margin-bottom: var(--ac-space-2);
  padding: var(--ac-space-2);
  background: var(--ac-bg-secondary);
  border-radius: var(--ac-radius-md);
  border: 1px solid var(--ac-border-light);
}

.form-row {
  margin-bottom: var(--ac-space-3);
}

.form-label {
  @extend %ac-label;
  display: block;
  margin-bottom: var(--ac-space-2);
}

.form-input {
  @extend %ac-input;
  width: 100%;

  &::placeholder {
    color: var(--ac-text-muted);
  }
}

.form-actions {
  display: flex;
  gap: var(--ac-space-3);
  margin-top: var(--ac-space-1);
  flex-wrap: wrap;
}

// ===== 按钮系统 =====
.btn, .action-btn {
  @include ac-button-base;
  @extend %ac-text-sm;
  padding: var(--ac-space-1) var(--ac-space-2);

  // 主要按钮
  &.btn-primary, &.primary {
    background: var(--ac-primary-600);
    color: var(--ac-text-inverse);
    box-shadow: var(--ac-shadow-sm);

    &:hover:not(:disabled) {
      background: var(--ac-primary-500);
      box-shadow: var(--ac-shadow-md);
    }
  }

  // 次要按钮
  &.btn-secondary, &.secondary {
    background: var(--ac-bg-secondary);
    color: var(--ac-text-primary);
    border-color: var(--ac-border-light);

    &:hover:not(:disabled) {
      background: var(--ac-bg-primary);
      border-color: var(--ac-border-medium);
    }
  }

  // 危险按钮
  &.btn-danger, &.danger {
    background: var(--ac-danger);
    color: var(--ac-text-inverse);
    box-shadow: var(--ac-shadow-sm);

    &:hover:not(:disabled) {
      background: #dc2626;
      box-shadow: var(--ac-shadow-md);
    }
  }

  // 小尺寸按钮
  &.btn-sm {
    padding: var(--ac-space-2) var(--ac-space-3);
    font-size: 0.75rem;
    border-radius: var(--ac-radius-sm);
  }
}

.btn-icon {
  font-size: 1rem;
}

.btn-text {
  display: inline;
}

.btn-group {
  display: flex;
  gap: var(--ac-space-3);
  margin-top: var(--ac-space-1);
  flex-wrap: wrap;
}

// ===== Token显示区域 =====
.token-display {
  margin-bottom: var(--ac-space-3);
}

.token-label {
  @extend %ac-label;
  margin-bottom: var(--ac-space-2);
}

.token-value {
  @extend %ac-code-block;
  @extend %ac-text-xs;
  color: var(--ac-text-secondary);
  word-break: break-all;
  cursor: pointer;
  transition: all var(--ac-duration-normal) var(--ac-ease-out);

  &:hover {
    background: var(--ac-bg-secondary);
    border-color: var(--ac-border-medium);
  }
}

// ===== 加载状态 =====
.loading {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--ac-border-light);
  border-top: 2px solid var(--ac-primary-500);
  border-radius: var(--ac-radius-full);
  animation: ac-spin 0.8s linear infinite;
}

@keyframes ac-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// ===== 请求监控面板 =====
.request-monitor {
  @extend %ac-card;
  padding: var(--ac-space-2);
}

.monitor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--ac-space-2);
  background: var(--ac-bg-secondary);
  border-radius: var(--ac-radius-md);
}

.monitor-stats {
  display: flex;
  gap: var(--ac-space-3);
  font-size: 0.75rem;
  color: var(--ac-text-secondary);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--ac-space-2);
  padding: var(--ac-space-2) var(--ac-space-3);
  background: var(--ac-bg-primary);
  border: 1px solid var(--ac-border-light);
  border-radius: var(--ac-radius-sm);
}

.stat-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--ac-radius-full);

  &.success {
    background: var(--ac-success);
  }
  &.error {
    background: var(--ac-danger);
  }
  &.pending {
    background: var(--ac-warning);
  }
}

.monitor-controls {
  display: flex;
  gap: var(--ac-space-2);
}

.request-filters {
  display: flex;
  gap: var(--ac-space-3);
  margin-bottom: var(--ac-space-2);
  background: var(--ac-bg-secondary);
  border-radius: var(--ac-radius-md);
}

.filter-input {
  @extend %ac-input;
  flex: 1;
  font-size: 0.75rem;
}

.filter-select {
  padding: var(--ac-space-2) var(--ac-space-3);
  border: 1px solid var(--ac-border-light);
  border-radius: var(--ac-radius-sm);
  font-size: 0.75rem;
  background: var(--ac-bg-primary);
  color: var(--ac-text-primary);
  transition: all 0.2s var(--ac-ease-out);
}

.request-list {
  overflow-y: auto;
  border: 1px solid var(--ac-border-light);
  border-radius: var(--ac-radius-md);
  background: var(--ac-bg-primary);
  @include ac-scrollbar;
}

.request-item {
  padding: var(--ac-space-3);
  border-bottom: 1px solid var(--ac-border-light);
  cursor: pointer;
  transition: all 0.2s var(--ac-ease-out);

  &:hover {
    background: var(--ac-bg-secondary);
  }

  &:last-child {
    border-bottom: none;
  }
}

.request-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.request-method {
  padding: var(--ac-space-1) var(--ac-space-2);
  border-radius: var(--ac-radius-sm);
  font-size: 0.625rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid transparent;
  transition: all 0.2s var(--ac-ease-out);
  color: white;

  &.GET {
    background: var(--ac-info);
  }
  &.POST {
    background: var(--ac-success);
  }
  &.PUT {
    background: var(--ac-warning);
  }
  &.DELETE {
    background: var(--ac-danger);
  }
  &.PATCH {
    background: var(--ac-primary-600);
  }
}

.request-status {
  display: flex;
  align-items: center;
  gap: var(--ac-space-2);
  font-size: 0.75rem;
}

.status-code {
  padding: var(--ac-space-1) var(--ac-space-2);
  border-radius: var(--ac-radius-sm);
  font-weight: 500;
  font-size: 0.625rem;
  border: 1px solid transparent;

  &.success {
    background: rgba(34, 197, 94, 0.1);
    color: var(--ac-success);
    border-color: rgba(34, 197, 94, 0.2);
  }
  &.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--ac-danger);
    border-color: rgba(239, 68, 68, 0.2);
  }
  &.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--ac-warning);
    border-color: rgba(245, 158, 11, 0.2);
  }
}

.request-time {
  color: var(--ac-text-muted);
  font-size: 0.625rem;
  background: var(--ac-bg-secondary);
  padding: var(--ac-space-1) var(--ac-space-2);
  border-radius: var(--ac-radius-sm);
}

.request-url {
  font-size: 0.75rem;
  color: var(--ac-text-primary);
  word-break: break-all;
  word-wrap: break-word;
  overflow-wrap: break-word;
  margin-bottom: var(--ac-space-2);
  overflow-x: hidden;
  max-width: 100%;
  font-family: var(--ac-font-mono);
  background: var(--ac-bg-secondary);
  padding: var(--ac-space-2);
  border-radius: var(--ac-radius-sm);
}

.request-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.625rem;
  color: var(--ac-text-secondary);
  margin-top: var(--ac-space-2);
}

.request-size,
.request-duration {
  background: var(--ac-bg-secondary);
  padding: var(--ac-space-1) var(--ac-space-2);
  border-radius: var(--ac-radius-sm);
  display: flex;
  align-items: center;
  gap: var(--ac-space-1);
}

.request-size::before {
  content: "📦";
  font-size: 0.75rem;
}

.request-duration::before {
  content: "⏱️";
  font-size: 0.75rem;
}

.empty-state {
  text-align: center;
  padding: var(--ac-space-8);
  color: var(--ac-text-secondary);
  background: var(--ac-bg-secondary);
  border-radius: var(--ac-radius-md);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: var(--ac-space-3);
  opacity: 0.5;
}

.empty-text {
  font-size: 0.875rem;
  font-weight: 500;
}

// ===== 请求详情弹窗 =====
.request-detail-modal .modal-overlay {
  z-index: var(--ac-z-modal-detail); // 确保在主弹窗之上
}

.request-detail-modal .modal {
  max-width: 800px;
}

.request-detail-modal .modal-body {
  overflow-y: auto;
}

.detail-content {
  padding: var(--ac-space-4);
  font-size: 0.875rem;
  line-height: 1.6;
}

.detail-row {
  display: flex;
  margin-bottom: var(--ac-space-3);
  border-bottom: 1px solid var(--ac-border-light);
  padding-bottom: var(--ac-space-2);
}

.detail-label {
  font-weight: 600;
  color: var(--ac-text-primary);
  min-width: 120px;
  flex-shrink: 0;
}

.detail-value {
  color: var(--ac-text-secondary);
  word-break: break-all;
  font-family: var(--ac-font-mono);
  font-size: 0.8rem;
}

// ===== Key-Value 列表样式 =====
.key-value-list {
  display: flex;
  flex-direction: column;
  gap: var(--ac-space-2);
}

.key-value-item {
  display: flex;
  align-items: flex-start;
  gap: var(--ac-space-3);
  padding: var(--ac-space-2) 0;
  border-bottom: 1px solid var(--ac-border-light);

  &:last-child {
    border-bottom: none;
  }
}

.key-value-item .key {
  font-weight: 600;
  color: var(--ac-text-primary);
  min-width: 120px;
  flex-shrink: 0;
  font-size: 0.875rem;
}

.key-value-item .value {
  color: var(--ac-text-secondary);
  font-family: var(--ac-font-mono);
  font-size: 0.8rem;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
  flex: 1;
  line-height: 1.5;
}

.json-content {
  background: var(--ac-bg-secondary);
  border: 1px solid var(--ac-border-light);
  border-radius: var(--ac-radius-md);
  padding: var(--ac-space-3);
  font-family: var(--ac-font-mono);
  font-size: 0.75rem;
  white-space: pre-wrap;
  word-break: break-all;
}
