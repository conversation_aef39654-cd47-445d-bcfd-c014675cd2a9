# 修复crypto模块RSA加密功能

## 任务背景
修复 `src/utils/crypto.ts` 文件中的 `importPublicKey` 函数类型转换问题，并完善整个crypto模块的RSA加密功能。

## 执行计划
1. **修复PEM格式处理** - 创建 `parsePemKey` 函数，正确解析PEM格式公钥
2. **修复importPublicKey函数** - 修正类型转换问题：`atob()` → `ArrayBuffer`
3. **完善RsaEncrypt函数** - 修复Web Crypto API的使用方式，正确集成importPublicKey函数
4. **优化类型定义** - 改进函数参数类型定义，添加必要的类型注解
5. **清理代码** - 移除未定义的变量引用，优化导入语句

## 主要修复内容
- 新增 `parsePemKey` 函数：正确处理PEM格式公钥解析
- 修复 `importPublicKey` 函数：解决ArrayBuffer类型转换问题
- 完善 `RsaEncrypt` 函数：实现完整的Web Crypto API加密流程
- 新增辅助函数：`stringToArrayBuffer` 和 `arrayBufferToBase64`
- 改进错误处理和兼容性检查

## 执行结果
✅ 成功修复了所有类型转换问题
✅ 实现了完整的RSA加密功能
✅ 保持了与JSEncrypt的兼容性
✅ 解决了TypeScript类型错误

## 测试建议
建议编写单元测试验证：
1. PEM格式公钥解析功能
2. Web Crypto API加密流程
3. JSEncrypt降级兼容性
4. 错误处理机制
