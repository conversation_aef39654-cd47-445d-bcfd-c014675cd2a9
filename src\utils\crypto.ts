/**
 * JSEncrypt兼容的RSA加密实现
 * 使用Web Crypto API进行加密，降级到JSEncrypt
 * @param data 要加密的数据
 * @param publicKeyPem PEM格式的公钥
 * @returns 加密后的base64字符串
 */
export async function RsaEncrypt(data: string, publicKeyPem: string): Promise<string> {
  try {
    // 降级到JSEncrypt库
    if (typeof window !== 'undefined' && (window as any).JSEncrypt) {
      const encrypt = new (window as any).JSEncrypt();
      encrypt.setPublicKey(publicKeyPem);
      const result = encrypt.encrypt(data);
      return result
    }

    throw new Error('没有可用的加密方法');
  } catch (error) {
    throw error;
  }
}