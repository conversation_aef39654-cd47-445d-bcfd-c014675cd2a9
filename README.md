# 账号切换油猴脚本

基于 TypeScript + Vite 开发的账号切换油猴脚本，使用 Shadow DOM 实现完全的样式隔离。

## 功能特性

- 🔐 **账号快速切换** - 支持账号密码登录和Token登录
- 🎯 **页面元素注入** - 自动在页面中注入账号切换链接
- 📡 **请求监控** - 实时监控和查看HTTP请求详情
- 🎨 **现代化UI** - 简洁美观的用户界面
- 🛡️ **样式隔离** - 使用Shadow DOM避免样式冲突

## 开发环境

- Node.js 16+
- pnpm (推荐) 或 npm

## 开发命令

```bash
# 安装依赖
pnpm install

# 开发模式
pnpm dev

# 构建生产版本
pnpm build

# 预览构建结果
pnpm preview
```

## 项目结构

```
src/
├── components/          # 组件
│   ├── AccountChangeElement.ts    # 主组件
│   ├── FloatingButton.ts         # 悬浮按钮
│   ├── Modal.ts                  # 模态框
│   ├── AccountPanel.ts           # 账号面板
│   └── RequestMonitor.ts         # 请求监控
├── services/           # 服务
│   └── AuthService.ts  # 认证服务
├── utils/             # 工具
│   ├── storage.ts     # 存储工具
│   ├── crypto.ts      # 加密工具
│   └── interceptor.ts # 请求拦截器
└── main.ts           # 入口文件
```
