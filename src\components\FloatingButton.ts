// 悬浮按钮组件

export class FloatingButton {
  private element!: HTMLElement;
  private onClick: () => void;

  constructor(onClick: () => void) {
    this.onClick = onClick;
    this.createElement();
    this.bindEvents();
  }

  /**
   * 创建按钮元素
   */
  private createElement(): void {
    this.element = document.createElement('button');
    this.element.className = 'floating-button';
    this.element.innerHTML = `
      <svg class="icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
      </svg>
    `;

    // CSS已经设置了正确的初始位置，不需要JavaScript干预
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 点击事件
    this.element.addEventListener('click', () => {
      this.onClick();
    });
  }

  /**
   * 显示按钮
   */
  show(): void {
    this.element.style.display = 'flex';
  }

  /**
   * 隐藏按钮
   */
  hide(): void {
    this.element.style.display = 'none';
  }

  /**
   * 设置加载状态
   */
  setLoading(loading: boolean): void {
    const button = this.element as HTMLButtonElement;
    if (loading) {
      button.innerHTML = `
        <div class="loading"></div>
      `;
      button.disabled = true;
    } else {
      button.innerHTML = `
        <svg class="icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
        </svg>
      `;
      button.disabled = false;
    }
  }

  /**
   * 获取DOM元素
   */
  getElement(): HTMLElement {
    return this.element;
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    // 移除DOM元素
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }
}
