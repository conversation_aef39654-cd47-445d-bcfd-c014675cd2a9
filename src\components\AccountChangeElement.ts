import { FloatingButton } from './FloatingButton';
import { Modal, TabContainer } from './Modal';
import { AccountPanel } from './AccountPanel';
import { RequestMonitor } from './RequestMonitor';
import { requestInterceptor } from '../utils/interceptor';
import { AuthService } from '../services/AuthService';
import { AccountStorage } from '../utils/storage';
import styles from '../styles/account-change.scss?inline';

/**
 * 账号切换主组件 - Shadow DOM实现
 * 使用Shadow DOM实现完全的样式隔离
 */
export class AccountChangeElement extends HTMLElement {
  private shadow: ShadowRoot;
  private floatingButton: FloatingButton | null = null;
  private modal: Modal | null = null;
  private tabContainer: TabContainer | null = null;
  private accountPanel: AccountPanel | null = null;
  private requestMonitor: RequestMonitor | null = null;
  private pageObserver: MutationObserver | null = null;

  constructor() {
    super();

    // 创建Shadow DOM
    this.shadow = this.attachShadow({ mode: 'closed' });

    // 初始化
    this.init();
  }

  /**
   * 初始化组件
   */
  private init(): void {
    // 注入样式
    this.injectStyles();

    // 启动请求拦截器
    requestInterceptor.start();

    // 创建悬浮按钮
    this.createFloatingButton();

    // 初始化页面元素监听
    this.initPageElementInjection();
  }

  /**
   * 注入样式到Shadow DOM
   */
  private injectStyles(): void {
    const style = document.createElement('style');
    style.textContent = styles;
    this.shadow.appendChild(style);
  }

  /**
   * 创建悬浮按钮
   */
  private createFloatingButton(): void {
    this.floatingButton = new FloatingButton(() => {
      this.showModal();
    });

    // 将悬浮按钮添加到Shadow DOM
    const buttonElement = this.floatingButton.getElement();
    this.shadow.appendChild(buttonElement);
  }

  /**
   * 显示主弹窗
   */
  private showModal(): void {
    if (this.modal) {
      this.modal.show();
      return;
    }

    this.modal = new Modal({
      showTitle: false,
      container: this.shadow, // 指定Shadow DOM作为容器
      onClose: () => {
        this.cleanup();
      }
    });

    // 创建标签页容器（标题栏模式）
    this.tabContainer = new TabContainer(true);

    // 获取标题栏的标签页容器
    const headerTabsContainer = this.modal.getModal().querySelector('.header-tabs') as HTMLElement;
    if (headerTabsContainer) {
      this.tabContainer.setTabsContainer(headerTabsContainer);
    }

    // 创建账号面板
    const panelState = {
      account: '',
      password: 'Authine@123456',
      token: '',
      loading: false
    };

    this.accountPanel = new AccountPanel(panelState, {
      onAccountLogin: async (account: string) => {
        try {
          this.accountPanel?.setLoading(true);

          await AuthService.login({
            account: account,
            password: 'Authine@123456'
          });

          this.accountPanel?.updateCurrentToken();
          this.showMessage('登录成功', 'success');

          // 可选：登录成功后刷新页面
          setTimeout(() => {
            AuthService.redirectAfterLogin();
          }, 1000);

        } catch (error) {
          this.showMessage('登录失败，请检查账号信息', 'error');
        } finally {
          this.accountPanel?.setLoading(false);
        }
      },
      onTokenLogin: (token: string) => {
        try {
          AuthService.tokenLogin(token);
          this.accountPanel?.updateCurrentToken();
          this.showMessage('Token登录成功', 'success');
        } catch (error) {
          this.showMessage('Token登录失败', 'error');
        }
      },
      onClearData: () => {
        try {
          AccountStorage.clearAll();
          this.accountPanel?.updateCurrentToken();
          this.showMessage('数据已清空', 'success');
        } catch (error) {
          this.showMessage('清空数据失败', 'error');
        }
      },
      onShowMessage: (message: string, type: 'success' | 'error' | 'warning') => {
        this.showMessage(message, type);
      }
    });

    // 创建请求监控
    this.requestMonitor = new RequestMonitor({
      onShowMessage: (message: string, type: 'success' | 'error' | 'warning') => {
        this.showMessage(message, type);
      }
    }, this.shadow);

    // 添加标签页
    this.tabContainer.addTab('account', '账号切换', this.accountPanel.getContainer());
    this.tabContainer.addTab('monitor', '请求监控', this.requestMonitor.getContainer());

    // 设置模态框内容
    this.modal.setContent(this.tabContainer.getContainer());

    // 显示模态框
    this.modal.show();
  }

  /**
   * 显示消息提示
   */
  private showMessage(message: string, type: 'success' | 'error' | 'warning' = 'success'): void {
    // 创建消息元素
    const messageEl = document.createElement('div');
    messageEl.className = `message message-${type}`;
    messageEl.textContent = message;

    // 设置样式
    messageEl.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 6px;
      color: white;
      font-size: 14px;
      z-index: 10003;
      opacity: 0;
      transform: translateX(100%);
      transition: all 0.3s ease;
      background: ${type === 'success' ? '#22c55e' : type === 'error' ? '#ef4444' : '#f59e0b'};
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    // 添加到Shadow DOM中
    this.shadow.appendChild(messageEl);

    // 显示动画
    setTimeout(() => {
      messageEl.style.opacity = '1';
      messageEl.style.transform = 'translateX(0)';
    }, 100);

    // 自动隐藏
    setTimeout(() => {
      messageEl.style.opacity = '0';
      messageEl.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (messageEl.parentNode) {
          messageEl.parentNode.removeChild(messageEl);
        }
      }, 300);
    }, 3000);
  }

  /**
   * 初始化页面元素注入功能
   */
  private initPageElementInjection(): void {
    // 立即检查并注入现有元素
    this.injectAccountSwitchLinks();

    // 设置DOM观察器监听页面变化
    this.setupPageObserver();
  }

  /**
   * 设置页面DOM观察器
   */
  private setupPageObserver(): void {
    this.pageObserver = new MutationObserver((mutations) => {
      let shouldCheck = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // 检查是否有新增的相关元素
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (element.querySelector?.('.author-card .userInfo-right-cn') ||
                  element.matches?.('.author-card .userInfo-right-cn') ||
                  element.closest?.('.author-card')) {
                shouldCheck = true;
              }
            }
          });
        }
      });

      if (shouldCheck) {
        // 延迟执行，确保DOM完全更新
        setTimeout(() => {
          this.injectAccountSwitchLinks();
        }, 100);
      }
    });

    // 开始观察整个文档的变化
    this.pageObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * 注入账号切换链接到页面元素
   */
  private injectAccountSwitchLinks(): void {
    let elements:any = document.querySelectorAll('.author-card .userInfo-right-cn:first-of-type');
    if(!elements) return

    for(let element of elements) {
       // 检查是否已存在账号切换链接
      const existingLink = element.querySelector('a[href="#"]');
      if (existingLink && existingLink.textContent === '账号切换') {
        continue; // 已存在，不重复添加
      }

      // 查找span元素
      const spanElement = element.querySelector('span');
      if (!spanElement) {
        continue;
      }

      // 创建账号切换链接
      const switchLink = this.createAccountSwitchLink(spanElement);

      // 将链接添加到元素中
      element.appendChild(switchLink);
    }
  }

  /**
   * 创建账号切换链接
   */
  private createAccountSwitchLink(spanElement: Element): HTMLElement {
    const link = document.createElement('a');
    link.href = '#';
    link.textContent = '账号切换';
    link.style.cssText = `
      margin-left: 8px;
      color: #0ea5e9;
      text-decoration: none;
      font-size: 12px;
      cursor: pointer;
      padding: 2px 6px;
      border-radius: 3px;
      border: 1px solid #0ea5e9;
      background: transparent;
      transition: all 0.2s ease;
      position: absolute;
      right: 20px;
    `;

    // 添加悬停效果
    link.addEventListener('mouseenter', () => {
      link.style.background = '#0ea5e9';
      link.style.color = 'white';
    });

    link.addEventListener('mouseleave', () => {
      link.style.background = 'transparent';
      link.style.color = '#0ea5e9';
    });

    // 添加点击事件
    link.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();

      // 提取账号信息
      const account = this.extractAccountFromSpan(spanElement);
      if (account) {
        this.handleQuickAccountSwitch(account);
      }
    });

    return link;
  }

  /**
   * 从span元素提取账号信息
   */
  private extractAccountFromSpan(spanElement: Element): string | null {
    const textContent = spanElement.textContent?.trim();
    if (!textContent) {
      return null;
    }

    // 这里可以根据实际情况调整账号提取逻辑
    // 目前直接返回span的文本内容
    return textContent;
  }

  /**
   * 处理快速账号切换
   */
  private async handleQuickAccountSwitch(account: string): Promise<void> {
    try {
      // 显示加载提示
      this.showMessage('正在切换账号...', 'warning');

      // 调用现有的账号登录功能
      await AuthService.login({
        account: account,
        password: 'Authine@123456'
      });

      this.showMessage(`账号 ${account} 登录成功`, 'success');

      // 登录成功后刷新页面
      setTimeout(() => {
        AuthService.redirectAfterLogin();
      }, 1000);

    } catch (error) {
      console.error('快速账号切换失败:', error);
      this.showMessage(`账号 ${account} 登录失败`, 'error');
    }
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    // 只清理模态框相关资源，保持页面注入功能
    this.modal = null;
    this.tabContainer = null;
    this.accountPanel = null;
    this.requestMonitor = null;
  }
}

// 注册自定义元素
customElements.define('account-change', AccountChangeElement);
