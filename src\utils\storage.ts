// 本地存储工具类

export interface StorageOptions {
  encrypt?: boolean;
  expiry?: number; // 过期时间（毫秒）
}

export class StorageUtil {
  private static readonly STORAGE_PREFIX = 'account_change_';
  private static readonly ENCRYPTION_KEY = 'ac_key_2024';

  /**
   * 设置存储项
   * @param key 键名
   * @param value 值
   * @param options 选项
   */
  static setItem(key: string, value: any, options: StorageOptions = {}): void {
    try {
      const fullKey = this.STORAGE_PREFIX + key;

      const data = {
        value,
        timestamp: Date.now(),
        expiry: options.expiry ? Date.now() + options.expiry : null
      };

      let serializedData = JSON.stringify(data);

      if (options.encrypt) {
        serializedData = this.encrypt(serializedData);
      }

      localStorage.setItem(fullKey, serializedData);
    } catch (error) {
      console.error('存储数据失败:', error);
    }
  }

  /**
   * 获取存储项
   * @param key 键名
   * @param defaultValue 默认值
   * @param encrypted 是否加密
   * @returns 存储的值
   */
  static getItem<T = any>(key: string, defaultValue: T | null = null, encrypted: boolean = false): T | null {
    try {
      const fullKey = this.STORAGE_PREFIX + key;
      let serializedData = localStorage.getItem(fullKey);

      if (!serializedData) {
        return defaultValue;
      }

      if (encrypted) {
        serializedData = this.decrypt(serializedData);
        if (!serializedData) {
          return defaultValue;
        }
      }

      const data = JSON.parse(serializedData);

      // 检查是否过期
      if (data.expiry && Date.now() > data.expiry) {
        this.removeItem(key);
        return defaultValue;
      }

      return data.value;
    } catch (error) {
      console.error('读取数据失败:', error);
      return defaultValue;
    }
  }

  /**
   * 移除存储项
   * @param key 键名
   */
  static removeItem(key: string): void {
    try {
      const fullKey = this.STORAGE_PREFIX + key;
      localStorage.removeItem(fullKey);
    } catch (error) {
      console.error('删除数据失败:', error);
    }
  }

  /**
   * 清空所有相关存储
   */
  static clear(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.STORAGE_PREFIX)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('清空数据失败:', error);
    }
  }

  /**
   * 获取所有相关存储项
   * @returns 存储项对象
   */
  static getAllItems(): Record<string, any> {
    const items: Record<string, any> = {};
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.STORAGE_PREFIX)) {
          const shortKey = key.replace(this.STORAGE_PREFIX, '');
          items[shortKey] = this.getItem(shortKey);
        }
      });
    } catch (error) {
      console.error('获取所有数据失败:', error);
    }
    return items;
  }

  /**
   * 检查存储项是否存在
   * @param key 键名
   * @returns 是否存在
   */
  static hasItem(key: string): boolean {
    const fullKey = this.STORAGE_PREFIX + key;
    return localStorage.getItem(fullKey) !== null;
  }

  /**
   * 获取存储大小（字节）
   * @returns 存储大小
   */
  static getStorageSize(): number {
    let size = 0;
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.STORAGE_PREFIX)) {
          const value = localStorage.getItem(key);
          if (value) {
            size += key.length + value.length;
          }
        }
      });
    } catch (error) {
      console.error('计算存储大小失败:', error);
    }
    return size;
  }

  /**
   * 简单加密
   * @param text 要加密的文本
   * @returns 加密后的文本
   */
  private static encrypt(text: string): string {
    let result = '';
    for (let i = 0; i < text.length; i++) {
      const charCode = text.charCodeAt(i) ^ this.ENCRYPTION_KEY.charCodeAt(i % this.ENCRYPTION_KEY.length);
      result += String.fromCharCode(charCode);
    }
    return btoa(result);
  }

  /**
   * 简单解密
   * @param encryptedText 加密的文本
   * @returns 解密后的文本
   */
  private static decrypt(encryptedText: string): string {
    try {
      const text = atob(encryptedText);
      let result = '';
      for (let i = 0; i < text.length; i++) {
        const charCode = text.charCodeAt(i) ^ this.ENCRYPTION_KEY.charCodeAt(i % this.ENCRYPTION_KEY.length);
        result += String.fromCharCode(charCode);
      }
      return result;
    } catch {
      return '';
    }
  }
}

// 特定的存储方法
export class AccountStorage {
  /**
   * 保存Token
   * @param token Token值
   */
  static saveToken(token: string): void {
    StorageUtil.setItem('token', token, { encrypt: true });
    // 同时保存到原始位置以保持兼容性
    localStorage.setItem('token', token);
  }

  /**
   * 获取Token
   * @returns Token值
   */
  static getToken(): string | null {
    // 优先从加密存储获取
    const encryptedToken = StorageUtil.getItem('token', null, true);
    if (encryptedToken) {
      return encryptedToken;
    }
    // 降级到原始存储
    return localStorage.getItem('token');
  }

  /**
   * 保存账号信息
   * @param account 账号
   */
  static saveAccount(account: string): void {
    StorageUtil.setItem('last_account', account);
  }

  /**
   * 获取上次使用的账号
   * @returns 账号
   */
  static getLastAccount(): string | null {
    return StorageUtil.getItem('last_account');
  }

  /**
   * 保存公司ID
   * @param corpId 公司ID
   */
  static saveCorpId(corpId: string): void {
    StorageUtil.setItem('corp_id', corpId);
    // 同时保存到原始位置以保持兼容性
    localStorage.setItem('corpId', corpId);
  }

  /**
   * 获取公司ID
   * @returns 公司ID
   */
  static getCorpId(): string | null {
    const corpId = StorageUtil.getItem('corp_id');
    if (corpId) {
      return corpId;
    }
    return localStorage.getItem('corpId');
  }

  /**
   * 保存用户设置
   * @param settings 设置对象
   */
  static saveSettings(settings: Record<string, any>): void {
    StorageUtil.setItem('user_settings', settings);
  }

  /**
   * 获取用户设置
   * @returns 设置对象
   */
  static getSettings(): Record<string, any> {
    return StorageUtil.getItem('user_settings', {}) || {};
  }

  /**
   * 清除所有账号相关数据
   */
  static clearAll(): void {
    StorageUtil.clear();
    // 清除原始存储
    localStorage.removeItem('token');
    localStorage.removeItem('corpId');
  }
}
