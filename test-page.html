<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号切换功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .author-card {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            background: #fafafa;
        }
        
        .userInfo-right-cn {
            display: flex;
            align-items: center;
            margin-top: 10px;
        }
        
        .userInfo-right-cn span {
            font-weight: bold;
            color: #333;
            background: #e8f4fd;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .add-card-btn {
            background: #0ea5e9;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        .add-card-btn:hover {
            background: #0284c7;
        }
        
        .info {
            background: #e0f2fe;
            border: 1px solid #0ea5e9;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .info h3 {
            margin-top: 0;
            color: #0369a1;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>账号切换功能测试页面</h1>
        
        <div class="info">
            <h3>测试说明</h3>
            <p>这个页面用于测试账号切换功能。页面会自动检测 <code>.author-card .userInfo-right-cn</code> 元素，并在其中注入"账号切换"链接。</p>
            <p>点击"账号切换"链接会自动提取span中的账号信息并尝试登录。</p>
        </div>
        
        <div class="author-card">
            <h4>作者卡片 1</h4>
            <p>这是一个示例作者卡片</p>
            <div class="userInfo-right-cn">
                <span data-v-63e672b>13730834091</span>
            </div>
        </div>
        
        <div class="author-card">
            <h4>作者卡片 2</h4>
            <p>另一个示例作者卡片</p>
            <div class="userInfo-right-cn">
                <span data-v-63e672b>1538045412</span>
            </div>
        </div>
        
        <div class="author-card">
            <h4>作者卡片 3</h4>
            <p>第三个示例作者卡片</p>
            <div class="userInfo-right-cn">
                <span data-v-63e672b>18888888888</span>
            </div>
        </div>
        
        <div style="margin-top: 30px;">
            <button class="add-card-btn" onclick="addNewCard()">动态添加新卡片</button>
            <button class="add-card-btn" onclick="removeCard()">移除最后一个卡片</button>
        </div>
        
        <div id="dynamic-cards"></div>
    </div>

    <script>
        let cardCounter = 4;
        
        function addNewCard() {
            const container = document.getElementById('dynamic-cards');
            const randomAccount = '1' + Math.floor(Math.random() * ********** + **********);
            
            const cardHtml = `
                <div class="author-card">
                    <h4>动态作者卡片 ${cardCounter}</h4>
                    <p>这是动态添加的作者卡片</p>
                    <div class="userInfo-right-cn">
                        <span data-v-63e672b>${randomAccount}</span>
                    </div>
                </div>
            `;
            
            container.insertAdjacentHTML('beforeend', cardHtml);
            cardCounter++;
        }
        
        function removeCard() {
            const container = document.getElementById('dynamic-cards');
            const lastCard = container.lastElementChild;
            if (lastCard) {
                lastCard.remove();
            }
        }
        
        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('测试页面已加载，账号切换功能应该已经激活');
            console.log('查看页面中的作者卡片，应该可以看到"账号切换"链接');
        });
    </script>
</body>
</html>
